#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "Pre-commit hook is running..."

# Run lint on staged files
staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|jsx|ts|tsx|vue)$' || true)

if [ -n "$staged_files" ]; then
  echo "Found staged files to lint:"
  echo "$staged_files"

  # Try to run eslint directly
  if command -v npx >/dev/null 2>&1; then
    echo "Running ESLint with npx..."
    echo "$staged_files" | xargs npx eslint --fix
  elif [ -f "./node_modules/.bin/eslint" ]; then
    echo "Running ESLint from node_modules..."
    echo "$staged_files" | xargs ./node_modules/.bin/eslint --fix
  else
    echo "ESLint not found, skipping lint check"
    exit 0
  fi

  # Add the fixed files back to staging
  echo "$staged_files" | xargs git add
  echo "Lint completed and files re-staged"
else
  echo "No JavaScript/TypeScript/Vue files to lint"
fi
