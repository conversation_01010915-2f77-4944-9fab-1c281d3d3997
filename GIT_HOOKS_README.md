# Git Hooks 配置说明

本项目已配置了 Git hooks 来在提交时自动运行代码检查和格式化。

## 功能说明

### Pre-commit Hook
- 在每次 `git commit` 时自动运行
- 检查暂存区中的 JavaScript、TypeScript 和 Vue 文件
- 自动运行 ESLint 进行代码检查和修复
- 将修复后的文件重新添加到暂存区

## 配置文件

### package.json
```json
{
  "scripts": {
    "lint": "eslint . --fix",
    "prepare": "husky"
  },
  "devDependencies": {
    "husky": "^9.0.11",
    "lint-staged": "^15.2.10"
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx,vue}": [
      "eslint --fix"
    ]
  }
}
```

### Git Hook 文件
- `.git/hooks/pre-commit` - 实际的 pre-commit hook 脚本
- `.husky/pre-commit` - Husky 配置文件（备用）

## 使用方法

1. **正常提交代码**：
   ```bash
   git add .
   git commit -m "Your commit message"
   ```

2. **Hook 会自动运行**：
   - 检查暂存的文件
   - 运行 ESLint 修复代码格式和问题
   - 重新暂存修复后的文件
   - 完成提交

## 输出示例

```
🔍 Pre-commit hook: Checking for linting...
📝 Found staged files to lint:
src/components/MyComponent.vue
🔧 Running ESLint from node_modules...
✅ ESLint completed successfully
📦 Fixed files re-staged
🎉 Pre-commit hook completed
```

## 故障排除

### 如果 ESLint 无法运行
Hook 会显示警告信息并继续提交：
```
⚠️  ESLint encountered issues (possibly due to Node.js version compatibility)
💡 Please run 'npm run lint' manually to check for issues
🚀 Continuing with commit...
```

在这种情况下，请手动运行：
```bash
npm run lint
```

### 跳过 Hook（不推荐）
如果需要跳过 pre-commit hook：
```bash
git commit -m "Your message" --no-verify
```

## 维护

### 更新 Hook
如果需要修改 hook 行为，编辑 `.git/hooks/pre-commit` 文件。

### 重新安装 Husky
如果 hooks 不工作，可以重新安装：
```bash
npm run prepare
```

## 注意事项

1. 确保项目中有 `node_modules` 目录和 ESLint 配置
2. Hook 只对暂存区的文件生效
3. 修复后的文件会自动重新暂存
4. 如果有严重的 lint 错误，提交可能会失败
