/* tslint:disable */
/* eslint-disable */
/**
 * Wallpapers API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 0.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from './configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
import type { RequestArgs } from './base';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, BaseAPI, RequiredError, operationServerMap } from './base';

/**
 * 
 * @export
 * @interface CategoriesColorsGet200Response
 */
export interface CategoriesColorsGet200Response {
    /**
     * 
     * @type {Array<CategoriesColorsGet200ResponseColorsInner>}
     * @memberof CategoriesColorsGet200Response
     */
    'colors': Array<CategoriesColorsGet200ResponseColorsInner>;
}
/**
 * 
 * @export
 * @interface CategoriesColorsGet200ResponseColorsInner
 */
export interface CategoriesColorsGet200ResponseColorsInner {
    /**
     * 
     * @type {string}
     * @memberof CategoriesColorsGet200ResponseColorsInner
     */
    'value': string;
    /**
     * 
     * @type {string}
     * @memberof CategoriesColorsGet200ResponseColorsInner
     */
    'RGB': string;
}
/**
 * 
 * @export
 * @interface CategoriesTagsGet200Response
 */
export interface CategoriesTagsGet200Response {
    /**
     * 
     * @type {Array<CategoriesTagsGet200ResponseTagsInner>}
     * @memberof CategoriesTagsGet200Response
     */
    'tags': Array<CategoriesTagsGet200ResponseTagsInner>;
}
/**
 * 
 * @export
 * @interface CategoriesTagsGet200ResponseTagsInner
 */
export interface CategoriesTagsGet200ResponseTagsInner {
    /**
     * 
     * @type {string}
     * @memberof CategoriesTagsGet200ResponseTagsInner
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof CategoriesTagsGet200ResponseTagsInner
     */
    'posterUrl': string | null;
}
/**
 * 
 * @export
 * @interface ClientsClientIdTopicsGet200Response
 */
export interface ClientsClientIdTopicsGet200Response {
    /**
     * 
     * @type {Array<ClientsClientIdTopicsGet200ResponseClientTopicsInner>}
     * @memberof ClientsClientIdTopicsGet200Response
     */
    'client_topics': Array<ClientsClientIdTopicsGet200ResponseClientTopicsInner>;
}
/**
 * 
 * @export
 * @interface ClientsClientIdTopicsGet200ResponseClientTopicsInner
 */
export interface ClientsClientIdTopicsGet200ResponseClientTopicsInner {
    /**
     * 
     * @type {number}
     * @memberof ClientsClientIdTopicsGet200ResponseClientTopicsInner
     */
    'id': number;
    /**
     * 
     * @type {string}
     * @memberof ClientsClientIdTopicsGet200ResponseClientTopicsInner
     */
    'title': string;
    /**
     * 
     * @type {number}
     * @memberof ClientsClientIdTopicsGet200ResponseClientTopicsInner
     */
    'topic_id': number;
}
/**
 * 
 * @export
 * @interface ClientsClientIdTopicsTopicIdWallpapersGet200Response
 */
export interface ClientsClientIdTopicsTopicIdWallpapersGet200Response {
    /**
     * 
     * @type {ClientsClientIdTopicsGet200ResponseClientTopicsInner}
     * @memberof ClientsClientIdTopicsTopicIdWallpapersGet200Response
     */
    'client_topic': ClientsClientIdTopicsGet200ResponseClientTopicsInner;
    /**
     * 
     * @type {Array<WallpapersGet200ResponseWallpapersInner>}
     * @memberof ClientsClientIdTopicsTopicIdWallpapersGet200Response
     */
    'wallpapers': Array<WallpapersGet200ResponseWallpapersInner>;
}
/**
 * 
 * @export
 * @interface DashAuthTokenPost200Response
 */
export interface DashAuthTokenPost200Response {
    /**
     * 
     * @type {string}
     * @memberof DashAuthTokenPost200Response
     */
    'access_token': string;
}
/**
 * 
 * @export
 * @interface DashAuthTokenPostRequest
 */
export interface DashAuthTokenPostRequest {
    /**
     * 
     * @type {string}
     * @memberof DashAuthTokenPostRequest
     */
    'username': string;
    /**
     * 
     * @type {string}
     * @memberof DashAuthTokenPostRequest
     */
    'password': string;
}
/**
 * 
 * @export
 * @interface DashClientsGet200Response
 */
export interface DashClientsGet200Response {
    /**
     * 
     * @type {Array<DashTopicpublishedGet200ResponsePublishedInnerClient>}
     * @memberof DashClientsGet200Response
     */
    'clients': Array<DashTopicpublishedGet200ResponsePublishedInnerClient>;
}
/**
 * 
 * @export
 * @interface DashSearchWallpapersGet200Response
 */
export interface DashSearchWallpapersGet200Response {
    /**
     * 
     * @type {string}
     * @memberof DashSearchWallpapersGet200Response
     */
    'q': string;
    /**
     * 
     * @type {Array<DashSearchWallpapersGet200ResponseResultsInner>}
     * @memberof DashSearchWallpapersGet200Response
     */
    'results': Array<DashSearchWallpapersGet200ResponseResultsInner>;
}
/**
 * 
 * @export
 * @interface DashSearchWallpapersGet200ResponseResultsInner
 */
export interface DashSearchWallpapersGet200ResponseResultsInner {
    /**
     * 
     * @type {number}
     * @memberof DashSearchWallpapersGet200ResponseResultsInner
     */
    'id': number;
    /**
     * 
     * @type {DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner}
     * @memberof DashSearchWallpapersGet200ResponseResultsInner
     */
    'wallpaper': DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner;
}
/**
 * 
 * @export
 * @interface DashTopicpublishedGet200Response
 */
export interface DashTopicpublishedGet200Response {
    /**
     * 
     * @type {Array<DashTopicpublishedGet200ResponsePublishedInner>}
     * @memberof DashTopicpublishedGet200Response
     */
    'published': Array<DashTopicpublishedGet200ResponsePublishedInner>;
    /**
     * 
     * @type {number}
     * @memberof DashTopicpublishedGet200Response
     */
    'current_page': number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicpublishedGet200Response
     */
    'current_page_size': number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicpublishedGet200Response
     */
    'total': number;
}
/**
 * 
 * @export
 * @interface DashTopicpublishedGet200ResponsePublishedInner
 */
export interface DashTopicpublishedGet200ResponsePublishedInner {
    /**
     * 
     * @type {number}
     * @memberof DashTopicpublishedGet200ResponsePublishedInner
     */
    'id': number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicpublishedGet200ResponsePublishedInner
     */
    'title': string;
    /**
     * 
     * @type {DashTopicsPost200Response}
     * @memberof DashTopicpublishedGet200ResponsePublishedInner
     */
    'topic': DashTopicsPost200Response;
    /**
     * 
     * @type {DashTopicpublishedGet200ResponsePublishedInnerClient}
     * @memberof DashTopicpublishedGet200ResponsePublishedInner
     */
    'client': DashTopicpublishedGet200ResponsePublishedInnerClient;
    /**
     * 
     * @type {string}
     * @memberof DashTopicpublishedGet200ResponsePublishedInner
     */
    'published_at': string;
}
/**
 * 
 * @export
 * @interface DashTopicpublishedGet200ResponsePublishedInnerClient
 */
export interface DashTopicpublishedGet200ResponsePublishedInnerClient {
    /**
     * 
     * @type {string}
     * @memberof DashTopicpublishedGet200ResponsePublishedInnerClient
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof DashTopicpublishedGet200ResponsePublishedInnerClient
     */
    'name': string;
}
/**
 * 
 * @export
 * @interface DashTopicpublishedPublishedIdPutRequest
 */
export interface DashTopicpublishedPublishedIdPutRequest {
    /**
     * 
     * @type {string}
     * @memberof DashTopicpublishedPublishedIdPutRequest
     */
    'title': string;
}
/**
 * 
 * @export
 * @interface DashTopicsGet200Response
 */
export interface DashTopicsGet200Response {
    /**
     * 
     * @type {Array<DashTopicsGet200ResponseTopicsInner>}
     * @memberof DashTopicsGet200Response
     */
    'topics': Array<DashTopicsGet200ResponseTopicsInner>;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsGet200Response
     */
    'current_page': number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsGet200Response
     */
    'current_page_size': number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsGet200Response
     */
    'total': number;
}
/**
 * 
 * @export
 * @interface DashTopicsGet200ResponseTopicsInner
 */
export interface DashTopicsGet200ResponseTopicsInner {
    /**
     * 
     * @type {number}
     * @memberof DashTopicsGet200ResponseTopicsInner
     */
    'id': number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsGet200ResponseTopicsInner
     */
    'comment': string;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsGet200ResponseTopicsInner
     */
    'wallpaper_count': number;
    /**
     * 已发布统计
     * @type {number}
     * @memberof DashTopicsGet200ResponseTopicsInner
     */
    'published_count': number;
}
/**
 * 
 * @export
 * @interface DashTopicsPost200Response
 */
export interface DashTopicsPost200Response {
    /**
     * 
     * @type {number}
     * @memberof DashTopicsPost200Response
     */
    'id': number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsPost200Response
     */
    'comment': string;
}
/**
 * 
 * @export
 * @interface DashTopicsPostRequest
 */
export interface DashTopicsPostRequest {
    /**
     * 
     * @type {string}
     * @memberof DashTopicsPostRequest
     */
    'comment': string;
}
/**
 * 
 * @export
 * @interface DashTopicsTopicIdSuggestionsTagsGet200Response
 */
export interface DashTopicsTopicIdSuggestionsTagsGet200Response {
    /**
     * 
     * @type {Array<string>}
     * @memberof DashTopicsTopicIdSuggestionsTagsGet200Response
     */
    'tags': Array<string>;
}
/**
 * 
 * @export
 * @interface DashTopicsTopicIdWallpapersGet200Response
 */
export interface DashTopicsTopicIdWallpapersGet200Response {
    /**
     * 
     * @type {DashTopicsPost200Response}
     * @memberof DashTopicsTopicIdWallpapersGet200Response
     */
    'topic': DashTopicsPost200Response;
    /**
     * 
     * @type {Array<DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner>}
     * @memberof DashTopicsTopicIdWallpapersGet200Response
     */
    'wallpapers': Array<DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner>;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200Response
     */
    'current_page': number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200Response
     */
    'current_page_size': number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200Response
     */
    'total': number;
}
/**
 * 
 * @export
 * @interface DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
 */
export interface DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner {
    /**
     * 
     * @type {WallpapersGet200ResponseWallpapersInnerImages}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    'images': WallpapersGet200ResponseWallpapersInnerImages;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    'format': string;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    'width': number;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    'height': number;
    /**
     * 单位为字节
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    'filesize': number;
    /**
     * 
     * @type {string}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    'content_md5': string;
    /**
     * 
     * @type {number}
     * @memberof DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner
     */
    'id': number;
}
/**
 * 
 * @export
 * @interface DashUploadwallpapersPostRequest
 */
export interface DashUploadwallpapersPostRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof DashUploadwallpapersPostRequest
     */
    'urls': Array<string>;
}
/**
 * 
 * @export
 * @interface DashUserinfoGet200Response
 */
export interface DashUserinfoGet200Response {
    /**
     * 
     * @type {string}
     * @memberof DashUserinfoGet200Response
     */
    'email': string;
}
/**
 * 
 * @export
 * @interface DashWallpapersGet200Response
 */
export interface DashWallpapersGet200Response {
    /**
     * 
     * @type {Array<DashWallpapersGet200ResponseWallpapersInner>}
     * @memberof DashWallpapersGet200Response
     */
    'wallpapers': Array<DashWallpapersGet200ResponseWallpapersInner>;
    /**
     * 
     * @type {number}
     * @memberof DashWallpapersGet200Response
     */
    'current_page': number;
    /**
     * 
     * @type {number}
     * @memberof DashWallpapersGet200Response
     */
    'current_page_size': number;
    /**
     * 
     * @type {number}
     * @memberof DashWallpapersGet200Response
     */
    'total': number;
}
/**
 * 
 * @export
 * @interface DashWallpapersGet200ResponseWallpapersInner
 */
export interface DashWallpapersGet200ResponseWallpapersInner {
    /**
     * 
     * @type {number}
     * @memberof DashWallpapersGet200ResponseWallpapersInner
     */
    'id': number;
    /**
     * 
     * @type {WallpapersGet200ResponseWallpapersInnerImages}
     * @memberof DashWallpapersGet200ResponseWallpapersInner
     */
    'images': WallpapersGet200ResponseWallpapersInnerImages;
    /**
     * 关联话题数量
     * @type {number}
     * @memberof DashWallpapersGet200ResponseWallpapersInner
     */
    'related_topic_count': number;
}
/**
 * 
 * @export
 * @interface DashWallpapersWallpaperIdTopicsGet200Response
 */
export interface DashWallpapersWallpaperIdTopicsGet200Response {
    /**
     * 
     * @type {Array<DashTopicsPost200Response>}
     * @memberof DashWallpapersWallpaperIdTopicsGet200Response
     */
    'topics': Array<DashTopicsPost200Response>;
}
/**
 * 
 * @export
 * @interface WallpapersGet200Response
 */
export interface WallpapersGet200Response {
    /**
     * 
     * @type {Array<WallpapersGet200ResponseWallpapersInner>}
     * @memberof WallpapersGet200Response
     */
    'wallpapers': Array<WallpapersGet200ResponseWallpapersInner>;
    /**
     * 
     * @type {number}
     * @memberof WallpapersGet200Response
     */
    'current_page': number;
    /**
     * 
     * @type {number}
     * @memberof WallpapersGet200Response
     */
    'current_page_size': number;
    /**
     * 
     * @type {number}
     * @memberof WallpapersGet200Response
     */
    'total': number;
}
/**
 * 
 * @export
 * @interface WallpapersGet200ResponseWallpapersInner
 */
export interface WallpapersGet200ResponseWallpapersInner {
    /**
     * 
     * @type {WallpapersGet200ResponseWallpapersInnerImages}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    'images': WallpapersGet200ResponseWallpapersInnerImages;
    /**
     * 
     * @type {string}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    'format': string;
    /**
     * 
     * @type {number}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    'width': number;
    /**
     * 
     * @type {number}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    'height': number;
    /**
     * 单位为字节
     * @type {number}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    'filesize': number;
    /**
     * 
     * @type {string}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    'content_md5': string;
    /**
     * 
     * @type {WallpapersGet200ResponseWallpapersInnerUploader}
     * @memberof WallpapersGet200ResponseWallpapersInner
     */
    'uploader': WallpapersGet200ResponseWallpapersInnerUploader;
}
/**
 * 图片列表，当未提供任何 size 时，将默认填充一个 default 字段
 * @export
 * @interface WallpapersGet200ResponseWallpapersInnerImages
 */
export interface WallpapersGet200ResponseWallpapersInnerImages {
    [key: string]: string | any;

    /**
     * 
     * @type {string}
     * @memberof WallpapersGet200ResponseWallpapersInnerImages
     */
    'default'?: string;
}
/**
 * 
 * @export
 * @interface WallpapersGet200ResponseWallpapersInnerUploader
 */
export interface WallpapersGet200ResponseWallpapersInnerUploader {
    /**
     * 
     * @type {string}
     * @memberof WallpapersGet200ResponseWallpapersInnerUploader
     */
    'name': string;
}
/**
 * 
 * @export
 * @interface WallpapersKeyRelatedGet200Response
 */
export interface WallpapersKeyRelatedGet200Response {
    /**
     * 
     * @type {Array<WallpapersGet200ResponseWallpapersInner>}
     * @memberof WallpapersKeyRelatedGet200Response
     */
    'related': Array<WallpapersGet200ResponseWallpapersInner>;
}

/**
 * DashboardApi - axios parameter creator
 * @export
 */
export const DashboardApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {string} cFTurnstileResponse 
         * @param {string} username 
         * @param {string} password 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashAuthTokenPost: async (cFTurnstileResponse: string, username: string, password: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'cFTurnstileResponse' is not null or undefined
            assertParamExists('dashAuthTokenPost', 'cFTurnstileResponse', cFTurnstileResponse)
            // verify required parameter 'username' is not null or undefined
            assertParamExists('dashAuthTokenPost', 'username', username)
            // verify required parameter 'password' is not null or undefined
            assertParamExists('dashAuthTokenPost', 'password', password)
            const localVarPath = `/dash/auth/token`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new URLSearchParams();


            if (username !== undefined) { 
                localVarFormParams.set('username', username as any);
            }
    
            if (password !== undefined) { 
                localVarFormParams.set('password', password as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'application/x-www-form-urlencoded';
    
            if (cFTurnstileResponse != null) {
                localVarHeaderParameter['CF-Turnstile-Response'] = String(cFTurnstileResponse);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams.toString();

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashAuthTokenRefreshGet: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/dash/auth/token/refresh`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashAuthTokenRevokeGet: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/dash/auth/token/revoke`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashClientsGet: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/dash/clients`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} q 
         * @param {number} [after] 
         * @param {number} [limit] 
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {number} [excludeTopicId] 排除指定专题下的壁纸
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashSearchWallpapersGet: async (q: string, after?: number, limit?: number, size?: Array<string>, excludeTopicId?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'q' is not null or undefined
            assertParamExists('dashSearchWallpapersGet', 'q', q)
            const localVarPath = `/dash/search/wallpapers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (q !== undefined) {
                localVarQueryParameter['q'] = q;
            }

            if (after !== undefined) {
                localVarQueryParameter['after'] = after;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }

            if (size) {
                localVarQueryParameter['size'] = size;
            }

            if (excludeTopicId !== undefined) {
                localVarQueryParameter['exclude_topic_id'] = excludeTopicId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 发布专题
         * @param {number} topicId 
         * @param {string} clientId 
         * @param {DashTopicpublishedPublishedIdPutRequest} dashTopicpublishedPublishedIdPutRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicTopicIdPublishClientIdPost: async (topicId: number, clientId: string, dashTopicpublishedPublishedIdPutRequest: DashTopicpublishedPublishedIdPutRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'topicId' is not null or undefined
            assertParamExists('dashTopicTopicIdPublishClientIdPost', 'topicId', topicId)
            // verify required parameter 'clientId' is not null or undefined
            assertParamExists('dashTopicTopicIdPublishClientIdPost', 'clientId', clientId)
            // verify required parameter 'dashTopicpublishedPublishedIdPutRequest' is not null or undefined
            assertParamExists('dashTopicTopicIdPublishClientIdPost', 'dashTopicpublishedPublishedIdPutRequest', dashTopicpublishedPublishedIdPutRequest)
            const localVarPath = `/dash/topic/{topic_id}/publish/{client_id}`
                .replace(`{${"topic_id"}}`, encodeURIComponent(String(topicId)))
                .replace(`{${"client_id"}}`, encodeURIComponent(String(clientId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(dashTopicpublishedPublishedIdPutRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicpublishedGet: async (page?: number, pageSize?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/dash/topicpublished`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['page_size'] = pageSize;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} publishedId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicpublishedPublishedIdDelete: async (publishedId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'publishedId' is not null or undefined
            assertParamExists('dashTopicpublishedPublishedIdDelete', 'publishedId', publishedId)
            const localVarPath = `/dash/topicpublished/{published_id}`
                .replace(`{${"published_id"}}`, encodeURIComponent(String(publishedId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} publishedId 
         * @param {DashTopicpublishedPublishedIdPutRequest} dashTopicpublishedPublishedIdPutRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicpublishedPublishedIdPut: async (publishedId: number, dashTopicpublishedPublishedIdPutRequest: DashTopicpublishedPublishedIdPutRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'publishedId' is not null or undefined
            assertParamExists('dashTopicpublishedPublishedIdPut', 'publishedId', publishedId)
            // verify required parameter 'dashTopicpublishedPublishedIdPutRequest' is not null or undefined
            assertParamExists('dashTopicpublishedPublishedIdPut', 'dashTopicpublishedPublishedIdPutRequest', dashTopicpublishedPublishedIdPutRequest)
            const localVarPath = `/dash/topicpublished/{published_id}`
                .replace(`{${"published_id"}}`, encodeURIComponent(String(publishedId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(dashTopicpublishedPublishedIdPutRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsGet: async (page?: number, pageSize?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/dash/topics`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['page_size'] = pageSize;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {DashTopicsPostRequest} dashTopicsPostRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsPost: async (dashTopicsPostRequest: DashTopicsPostRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'dashTopicsPostRequest' is not null or undefined
            assertParamExists('dashTopicsPost', 'dashTopicsPostRequest', dashTopicsPostRequest)
            const localVarPath = `/dash/topics`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(dashTopicsPostRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} topicId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdDelete: async (topicId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'topicId' is not null or undefined
            assertParamExists('dashTopicsTopicIdDelete', 'topicId', topicId)
            const localVarPath = `/dash/topics/{topic_id}`
                .replace(`{${"topic_id"}}`, encodeURIComponent(String(topicId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} topicId 
         * @param {DashTopicsPostRequest} dashTopicsPostRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdPut: async (topicId: number, dashTopicsPostRequest: DashTopicsPostRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'topicId' is not null or undefined
            assertParamExists('dashTopicsTopicIdPut', 'topicId', topicId)
            // verify required parameter 'dashTopicsPostRequest' is not null or undefined
            assertParamExists('dashTopicsTopicIdPut', 'dashTopicsPostRequest', dashTopicsPostRequest)
            const localVarPath = `/dash/topics/{topic_id}`
                .replace(`{${"topic_id"}}`, encodeURIComponent(String(topicId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(dashTopicsPostRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取 Topic 可能的标签
         * @param {number} topicId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdSuggestionsTagsGet: async (topicId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'topicId' is not null or undefined
            assertParamExists('dashTopicsTopicIdSuggestionsTagsGet', 'topicId', topicId)
            const localVarPath = `/dash/topics/{topic_id}/suggestions/tags`
                .replace(`{${"topic_id"}}`, encodeURIComponent(String(topicId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} topicId 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdWallpapersGet: async (topicId: number, page?: number, pageSize?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'topicId' is not null or undefined
            assertParamExists('dashTopicsTopicIdWallpapersGet', 'topicId', topicId)
            const localVarPath = `/dash/topics/{topic_id}/wallpapers`
                .replace(`{${"topic_id"}}`, encodeURIComponent(String(topicId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['page_size'] = pageSize;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 取消壁纸与话题的关联
         * @param {number} topicId 
         * @param {number} wallpaperId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdWallpapersWallpaperIdDelete: async (topicId: number, wallpaperId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'topicId' is not null or undefined
            assertParamExists('dashTopicsTopicIdWallpapersWallpaperIdDelete', 'topicId', topicId)
            // verify required parameter 'wallpaperId' is not null or undefined
            assertParamExists('dashTopicsTopicIdWallpapersWallpaperIdDelete', 'wallpaperId', wallpaperId)
            const localVarPath = `/dash/topics/{topic_id}/wallpapers/{wallpaper_id}`
                .replace(`{${"topic_id"}}`, encodeURIComponent(String(topicId)))
                .replace(`{${"wallpaper_id"}}`, encodeURIComponent(String(wallpaperId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 将壁纸与话题关联
         * @param {number} topicId 
         * @param {number} wallpaperId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdWallpapersWallpaperIdPut: async (topicId: number, wallpaperId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'topicId' is not null or undefined
            assertParamExists('dashTopicsTopicIdWallpapersWallpaperIdPut', 'topicId', topicId)
            // verify required parameter 'wallpaperId' is not null or undefined
            assertParamExists('dashTopicsTopicIdWallpapersWallpaperIdPut', 'wallpaperId', wallpaperId)
            const localVarPath = `/dash/topics/{topic_id}/wallpapers/{wallpaper_id}`
                .replace(`{${"topic_id"}}`, encodeURIComponent(String(topicId)))
                .replace(`{${"wallpaper_id"}}`, encodeURIComponent(String(wallpaperId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {DashUploadwallpapersPostRequest} dashUploadwallpapersPostRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashUploadwallpapersPost: async (dashUploadwallpapersPostRequest: DashUploadwallpapersPostRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'dashUploadwallpapersPostRequest' is not null or undefined
            assertParamExists('dashUploadwallpapersPost', 'dashUploadwallpapersPostRequest', dashUploadwallpapersPostRequest)
            const localVarPath = `/dash/uploadwallpapers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(dashUploadwallpapersPostRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashUserinfoGet: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/dash/userinfo`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashWallpapersGet: async (page?: number, pageSize?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/dash/wallpapers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['page_size'] = pageSize;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {number} wallpaperId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashWallpapersWallpaperIdTopicsGet: async (wallpaperId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'wallpaperId' is not null or undefined
            assertParamExists('dashWallpapersWallpaperIdTopicsGet', 'wallpaperId', wallpaperId)
            const localVarPath = `/dash/wallpapers/{wallpaper_id}/topics`
                .replace(`{${"wallpaper_id"}}`, encodeURIComponent(String(wallpaperId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication access_token required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DashboardApi - functional programming interface
 * @export
 */
export const DashboardApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DashboardApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {string} cFTurnstileResponse 
         * @param {string} username 
         * @param {string} password 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashAuthTokenPost(cFTurnstileResponse: string, username: string, password: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashAuthTokenPost200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashAuthTokenPost(cFTurnstileResponse, username, password, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashAuthTokenPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashAuthTokenRefreshGet(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashAuthTokenPost200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashAuthTokenRefreshGet(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashAuthTokenRefreshGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashAuthTokenRevokeGet(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashAuthTokenRevokeGet(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashAuthTokenRevokeGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashClientsGet(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashClientsGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashClientsGet(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashClientsGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} q 
         * @param {number} [after] 
         * @param {number} [limit] 
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {number} [excludeTopicId] 排除指定专题下的壁纸
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashSearchWallpapersGet(q: string, after?: number, limit?: number, size?: Array<string>, excludeTopicId?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashSearchWallpapersGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashSearchWallpapersGet(q, after, limit, size, excludeTopicId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashSearchWallpapersGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 发布专题
         * @param {number} topicId 
         * @param {string} clientId 
         * @param {DashTopicpublishedPublishedIdPutRequest} dashTopicpublishedPublishedIdPutRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicTopicIdPublishClientIdPost(topicId: number, clientId: string, dashTopicpublishedPublishedIdPutRequest: DashTopicpublishedPublishedIdPutRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicTopicIdPublishClientIdPost(topicId, clientId, dashTopicpublishedPublishedIdPutRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicTopicIdPublishClientIdPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicpublishedGet(page?: number, pageSize?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashTopicpublishedGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicpublishedGet(page, pageSize, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicpublishedGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} publishedId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicpublishedPublishedIdDelete(publishedId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicpublishedPublishedIdDelete(publishedId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicpublishedPublishedIdDelete']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} publishedId 
         * @param {DashTopicpublishedPublishedIdPutRequest} dashTopicpublishedPublishedIdPutRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicpublishedPublishedIdPut(publishedId: number, dashTopicpublishedPublishedIdPutRequest: DashTopicpublishedPublishedIdPutRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicpublishedPublishedIdPut(publishedId, dashTopicpublishedPublishedIdPutRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicpublishedPublishedIdPut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicsGet(page?: number, pageSize?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashTopicsGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicsGet(page, pageSize, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicsGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {DashTopicsPostRequest} dashTopicsPostRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicsPost(dashTopicsPostRequest: DashTopicsPostRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashTopicsPost200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicsPost(dashTopicsPostRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicsPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} topicId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicsTopicIdDelete(topicId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicsTopicIdDelete(topicId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicsTopicIdDelete']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} topicId 
         * @param {DashTopicsPostRequest} dashTopicsPostRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicsTopicIdPut(topicId: number, dashTopicsPostRequest: DashTopicsPostRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashTopicsPost200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicsTopicIdPut(topicId, dashTopicsPostRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicsTopicIdPut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 获取 Topic 可能的标签
         * @param {number} topicId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicsTopicIdSuggestionsTagsGet(topicId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashTopicsTopicIdSuggestionsTagsGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicsTopicIdSuggestionsTagsGet(topicId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicsTopicIdSuggestionsTagsGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} topicId 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicsTopicIdWallpapersGet(topicId: number, page?: number, pageSize?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashTopicsTopicIdWallpapersGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicsTopicIdWallpapersGet(topicId, page, pageSize, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicsTopicIdWallpapersGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 取消壁纸与话题的关联
         * @param {number} topicId 
         * @param {number} wallpaperId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicsTopicIdWallpapersWallpaperIdDelete(topicId: number, wallpaperId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicsTopicIdWallpapersWallpaperIdDelete(topicId, wallpaperId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicsTopicIdWallpapersWallpaperIdDelete']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 将壁纸与话题关联
         * @param {number} topicId 
         * @param {number} wallpaperId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashTopicsTopicIdWallpapersWallpaperIdPut(topicId: number, wallpaperId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashTopicsTopicIdWallpapersWallpaperIdPut(topicId, wallpaperId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashTopicsTopicIdWallpapersWallpaperIdPut']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {DashUploadwallpapersPostRequest} dashUploadwallpapersPostRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashUploadwallpapersPost(dashUploadwallpapersPostRequest: DashUploadwallpapersPostRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashUploadwallpapersPost(dashUploadwallpapersPostRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashUploadwallpapersPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashUserinfoGet(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashUserinfoGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashUserinfoGet(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashUserinfoGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashWallpapersGet(page?: number, pageSize?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashWallpapersGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashWallpapersGet(page, pageSize, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashWallpapersGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {number} wallpaperId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async dashWallpapersWallpaperIdTopicsGet(wallpaperId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<DashWallpapersWallpaperIdTopicsGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.dashWallpapersWallpaperIdTopicsGet(wallpaperId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DashboardApi.dashWallpapersWallpaperIdTopicsGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DashboardApi - factory interface
 * @export
 */
export const DashboardApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DashboardApiFp(configuration)
    return {
        /**
         * 
         * @param {string} cFTurnstileResponse 
         * @param {string} username 
         * @param {string} password 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashAuthTokenPost(cFTurnstileResponse: string, username: string, password: string, options?: RawAxiosRequestConfig): AxiosPromise<DashAuthTokenPost200Response> {
            return localVarFp.dashAuthTokenPost(cFTurnstileResponse, username, password, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashAuthTokenRefreshGet(options?: RawAxiosRequestConfig): AxiosPromise<DashAuthTokenPost200Response> {
            return localVarFp.dashAuthTokenRefreshGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashAuthTokenRevokeGet(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.dashAuthTokenRevokeGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashClientsGet(options?: RawAxiosRequestConfig): AxiosPromise<DashClientsGet200Response> {
            return localVarFp.dashClientsGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} q 
         * @param {number} [after] 
         * @param {number} [limit] 
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {number} [excludeTopicId] 排除指定专题下的壁纸
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashSearchWallpapersGet(q: string, after?: number, limit?: number, size?: Array<string>, excludeTopicId?: number, options?: RawAxiosRequestConfig): AxiosPromise<DashSearchWallpapersGet200Response> {
            return localVarFp.dashSearchWallpapersGet(q, after, limit, size, excludeTopicId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 发布专题
         * @param {number} topicId 
         * @param {string} clientId 
         * @param {DashTopicpublishedPublishedIdPutRequest} dashTopicpublishedPublishedIdPutRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicTopicIdPublishClientIdPost(topicId: number, clientId: string, dashTopicpublishedPublishedIdPutRequest: DashTopicpublishedPublishedIdPutRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.dashTopicTopicIdPublishClientIdPost(topicId, clientId, dashTopicpublishedPublishedIdPutRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicpublishedGet(page?: number, pageSize?: number, options?: RawAxiosRequestConfig): AxiosPromise<DashTopicpublishedGet200Response> {
            return localVarFp.dashTopicpublishedGet(page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} publishedId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicpublishedPublishedIdDelete(publishedId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.dashTopicpublishedPublishedIdDelete(publishedId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} publishedId 
         * @param {DashTopicpublishedPublishedIdPutRequest} dashTopicpublishedPublishedIdPutRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicpublishedPublishedIdPut(publishedId: number, dashTopicpublishedPublishedIdPutRequest: DashTopicpublishedPublishedIdPutRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.dashTopicpublishedPublishedIdPut(publishedId, dashTopicpublishedPublishedIdPutRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsGet(page?: number, pageSize?: number, options?: RawAxiosRequestConfig): AxiosPromise<DashTopicsGet200Response> {
            return localVarFp.dashTopicsGet(page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {DashTopicsPostRequest} dashTopicsPostRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsPost(dashTopicsPostRequest: DashTopicsPostRequest, options?: RawAxiosRequestConfig): AxiosPromise<DashTopicsPost200Response> {
            return localVarFp.dashTopicsPost(dashTopicsPostRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} topicId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdDelete(topicId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.dashTopicsTopicIdDelete(topicId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} topicId 
         * @param {DashTopicsPostRequest} dashTopicsPostRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdPut(topicId: number, dashTopicsPostRequest: DashTopicsPostRequest, options?: RawAxiosRequestConfig): AxiosPromise<DashTopicsPost200Response> {
            return localVarFp.dashTopicsTopicIdPut(topicId, dashTopicsPostRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取 Topic 可能的标签
         * @param {number} topicId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdSuggestionsTagsGet(topicId: number, options?: RawAxiosRequestConfig): AxiosPromise<DashTopicsTopicIdSuggestionsTagsGet200Response> {
            return localVarFp.dashTopicsTopicIdSuggestionsTagsGet(topicId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} topicId 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdWallpapersGet(topicId: number, page?: number, pageSize?: number, options?: RawAxiosRequestConfig): AxiosPromise<DashTopicsTopicIdWallpapersGet200Response> {
            return localVarFp.dashTopicsTopicIdWallpapersGet(topicId, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 取消壁纸与话题的关联
         * @param {number} topicId 
         * @param {number} wallpaperId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdWallpapersWallpaperIdDelete(topicId: number, wallpaperId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.dashTopicsTopicIdWallpapersWallpaperIdDelete(topicId, wallpaperId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 将壁纸与话题关联
         * @param {number} topicId 
         * @param {number} wallpaperId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashTopicsTopicIdWallpapersWallpaperIdPut(topicId: number, wallpaperId: number, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.dashTopicsTopicIdWallpapersWallpaperIdPut(topicId, wallpaperId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {DashUploadwallpapersPostRequest} dashUploadwallpapersPostRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashUploadwallpapersPost(dashUploadwallpapersPostRequest: DashUploadwallpapersPostRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.dashUploadwallpapersPost(dashUploadwallpapersPostRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashUserinfoGet(options?: RawAxiosRequestConfig): AxiosPromise<DashUserinfoGet200Response> {
            return localVarFp.dashUserinfoGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashWallpapersGet(page?: number, pageSize?: number, options?: RawAxiosRequestConfig): AxiosPromise<DashWallpapersGet200Response> {
            return localVarFp.dashWallpapersGet(page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {number} wallpaperId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        dashWallpapersWallpaperIdTopicsGet(wallpaperId: number, options?: RawAxiosRequestConfig): AxiosPromise<DashWallpapersWallpaperIdTopicsGet200Response> {
            return localVarFp.dashWallpapersWallpaperIdTopicsGet(wallpaperId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DashboardApi - object-oriented interface
 * @export
 * @class DashboardApi
 * @extends {BaseAPI}
 */
export class DashboardApi extends BaseAPI {
    /**
     * 
     * @param {string} cFTurnstileResponse 
     * @param {string} username 
     * @param {string} password 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashAuthTokenPost(cFTurnstileResponse: string, username: string, password: string, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashAuthTokenPost(cFTurnstileResponse, username, password, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashAuthTokenRefreshGet(options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashAuthTokenRefreshGet(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashAuthTokenRevokeGet(options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashAuthTokenRevokeGet(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashClientsGet(options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashClientsGet(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} q 
     * @param {number} [after] 
     * @param {number} [limit] 
     * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
     * @param {number} [excludeTopicId] 排除指定专题下的壁纸
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashSearchWallpapersGet(q: string, after?: number, limit?: number, size?: Array<string>, excludeTopicId?: number, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashSearchWallpapersGet(q, after, limit, size, excludeTopicId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 发布专题
     * @param {number} topicId 
     * @param {string} clientId 
     * @param {DashTopicpublishedPublishedIdPutRequest} dashTopicpublishedPublishedIdPutRequest 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicTopicIdPublishClientIdPost(topicId: number, clientId: string, dashTopicpublishedPublishedIdPutRequest: DashTopicpublishedPublishedIdPutRequest, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicTopicIdPublishClientIdPost(topicId, clientId, dashTopicpublishedPublishedIdPutRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicpublishedGet(page?: number, pageSize?: number, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicpublishedGet(page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} publishedId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicpublishedPublishedIdDelete(publishedId: number, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicpublishedPublishedIdDelete(publishedId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} publishedId 
     * @param {DashTopicpublishedPublishedIdPutRequest} dashTopicpublishedPublishedIdPutRequest 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicpublishedPublishedIdPut(publishedId: number, dashTopicpublishedPublishedIdPutRequest: DashTopicpublishedPublishedIdPutRequest, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicpublishedPublishedIdPut(publishedId, dashTopicpublishedPublishedIdPutRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicsGet(page?: number, pageSize?: number, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicsGet(page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {DashTopicsPostRequest} dashTopicsPostRequest 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicsPost(dashTopicsPostRequest: DashTopicsPostRequest, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicsPost(dashTopicsPostRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} topicId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicsTopicIdDelete(topicId: number, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicsTopicIdDelete(topicId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} topicId 
     * @param {DashTopicsPostRequest} dashTopicsPostRequest 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicsTopicIdPut(topicId: number, dashTopicsPostRequest: DashTopicsPostRequest, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicsTopicIdPut(topicId, dashTopicsPostRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 获取 Topic 可能的标签
     * @param {number} topicId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicsTopicIdSuggestionsTagsGet(topicId: number, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicsTopicIdSuggestionsTagsGet(topicId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} topicId 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicsTopicIdWallpapersGet(topicId: number, page?: number, pageSize?: number, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicsTopicIdWallpapersGet(topicId, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 取消壁纸与话题的关联
     * @param {number} topicId 
     * @param {number} wallpaperId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicsTopicIdWallpapersWallpaperIdDelete(topicId: number, wallpaperId: number, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicsTopicIdWallpapersWallpaperIdDelete(topicId, wallpaperId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 将壁纸与话题关联
     * @param {number} topicId 
     * @param {number} wallpaperId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashTopicsTopicIdWallpapersWallpaperIdPut(topicId: number, wallpaperId: number, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashTopicsTopicIdWallpapersWallpaperIdPut(topicId, wallpaperId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {DashUploadwallpapersPostRequest} dashUploadwallpapersPostRequest 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashUploadwallpapersPost(dashUploadwallpapersPostRequest: DashUploadwallpapersPostRequest, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashUploadwallpapersPost(dashUploadwallpapersPostRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashUserinfoGet(options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashUserinfoGet(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashWallpapersGet(page?: number, pageSize?: number, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashWallpapersGet(page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {number} wallpaperId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DashboardApi
     */
    public dashWallpapersWallpaperIdTopicsGet(wallpaperId: number, options?: RawAxiosRequestConfig) {
        return DashboardApiFp(this.configuration).dashWallpapersWallpaperIdTopicsGet(wallpaperId, options).then((request) => request(this.axios, this.basePath));
    }
}



/**
 * DefaultApi - axios parameter creator
 * @export
 */
export const DefaultApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        categoriesColorsGet: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/categories/colors`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        categoriesTagsGet: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/categories/tags`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} clientId 
         * @param {string} [group] 
         * @param {number} [after] 使用 client_topic.id，而不是 topic_id
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clientsClientIdTopicsGet: async (clientId: string, group?: string, after?: number, limit?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'clientId' is not null or undefined
            assertParamExists('clientsClientIdTopicsGet', 'clientId', clientId)
            const localVarPath = `/clients/{client_id}/topics`
                .replace(`{${"client_id"}}`, encodeURIComponent(String(clientId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (group !== undefined) {
                localVarQueryParameter['group'] = group;
            }

            if (after !== undefined) {
                localVarQueryParameter['after'] = after;
            }

            if (limit !== undefined) {
                localVarQueryParameter['limit'] = limit;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} clientId 
         * @param {number} topicId 
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clientsClientIdTopicsTopicIdWallpapersGet: async (clientId: string, topicId: number, size?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'clientId' is not null or undefined
            assertParamExists('clientsClientIdTopicsTopicIdWallpapersGet', 'clientId', clientId)
            // verify required parameter 'topicId' is not null or undefined
            assertParamExists('clientsClientIdTopicsTopicIdWallpapersGet', 'topicId', topicId)
            const localVarPath = `/clients/{client_id}/topics/{topic_id}/wallpapers`
                .replace(`{${"client_id"}}`, encodeURIComponent(String(clientId)))
                .replace(`{${"topic_id"}}`, encodeURIComponent(String(topicId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (size) {
                localVarQueryParameter['size'] = size;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取原始图片
         * @param {string} key 
         * @param {string} cFTurnstileResponse 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageKeyGet: async (key: string, cFTurnstileResponse: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('imageKeyGet', 'key', key)
            // verify required parameter 'cFTurnstileResponse' is not null or undefined
            assertParamExists('imageKeyGet', 'cFTurnstileResponse', cFTurnstileResponse)
            const localVarPath = `/image/{key}`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (cFTurnstileResponse != null) {
                localVarHeaderParameter['CF-Turnstile-Response'] = String(cFTurnstileResponse);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 图片为等比例缩放
         * @summary 获取壁纸列表，不包含原始图片地址，所以作为预览展示使用。
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {WallpapersGetSizedForEnum} [sizedFor] [filter] 根据尺寸进行过滤
         * @param {string} [color] [filter] 根据色系进行过滤
         * @param {string} [tag] [filter] 根据标签进行过滤
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        wallpapersGet: async (size?: Array<string>, page?: number, pageSize?: number, sizedFor?: WallpapersGetSizedForEnum, color?: string, tag?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/wallpapers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (size) {
                localVarQueryParameter['size'] = size;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['page_size'] = pageSize;
            }

            if (sizedFor !== undefined) {
                localVarQueryParameter['sized_for'] = sizedFor;
            }

            if (color !== undefined) {
                localVarQueryParameter['color'] = color;
            }

            if (tag !== undefined) {
                localVarQueryParameter['tag'] = tag;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取当前壁纸的相关其他壁纸 (固定数量的)
         * @param {string} key 使用 content_md5 值
         * @param {number} [num] 返回的数据量最大条数
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        wallpapersKeyRelatedGet: async (key: string, num?: number, size?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'key' is not null or undefined
            assertParamExists('wallpapersKeyRelatedGet', 'key', key)
            const localVarPath = `/wallpapers/{key}/related`
                .replace(`{${"key"}}`, encodeURIComponent(String(key)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (num !== undefined) {
                localVarQueryParameter['num'] = num;
            }

            if (size) {
                localVarQueryParameter['size'] = size;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DefaultApi - functional programming interface
 * @export
 */
export const DefaultApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DefaultApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async categoriesColorsGet(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CategoriesColorsGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.categoriesColorsGet(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DefaultApi.categoriesColorsGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async categoriesTagsGet(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CategoriesTagsGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.categoriesTagsGet(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DefaultApi.categoriesTagsGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} clientId 
         * @param {string} [group] 
         * @param {number} [after] 使用 client_topic.id，而不是 topic_id
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async clientsClientIdTopicsGet(clientId: string, group?: string, after?: number, limit?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ClientsClientIdTopicsGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.clientsClientIdTopicsGet(clientId, group, after, limit, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DefaultApi.clientsClientIdTopicsGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} clientId 
         * @param {number} topicId 
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async clientsClientIdTopicsTopicIdWallpapersGet(clientId: string, topicId: number, size?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ClientsClientIdTopicsTopicIdWallpapersGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.clientsClientIdTopicsTopicIdWallpapersGet(clientId, topicId, size, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DefaultApi.clientsClientIdTopicsTopicIdWallpapersGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 获取原始图片
         * @param {string} key 
         * @param {string} cFTurnstileResponse 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async imageKeyGet(key: string, cFTurnstileResponse: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.imageKeyGet(key, cFTurnstileResponse, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DefaultApi.imageKeyGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 图片为等比例缩放
         * @summary 获取壁纸列表，不包含原始图片地址，所以作为预览展示使用。
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {WallpapersGetSizedForEnum} [sizedFor] [filter] 根据尺寸进行过滤
         * @param {string} [color] [filter] 根据色系进行过滤
         * @param {string} [tag] [filter] 根据标签进行过滤
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async wallpapersGet(size?: Array<string>, page?: number, pageSize?: number, sizedFor?: WallpapersGetSizedForEnum, color?: string, tag?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<WallpapersGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.wallpapersGet(size, page, pageSize, sizedFor, color, tag, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DefaultApi.wallpapersGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 获取当前壁纸的相关其他壁纸 (固定数量的)
         * @param {string} key 使用 content_md5 值
         * @param {number} [num] 返回的数据量最大条数
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async wallpapersKeyRelatedGet(key: string, num?: number, size?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<WallpapersKeyRelatedGet200Response>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.wallpapersKeyRelatedGet(key, num, size, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DefaultApi.wallpapersKeyRelatedGet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DefaultApi - factory interface
 * @export
 */
export const DefaultApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DefaultApiFp(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        categoriesColorsGet(options?: RawAxiosRequestConfig): AxiosPromise<CategoriesColorsGet200Response> {
            return localVarFp.categoriesColorsGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        categoriesTagsGet(options?: RawAxiosRequestConfig): AxiosPromise<CategoriesTagsGet200Response> {
            return localVarFp.categoriesTagsGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} clientId 
         * @param {string} [group] 
         * @param {number} [after] 使用 client_topic.id，而不是 topic_id
         * @param {number} [limit] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clientsClientIdTopicsGet(clientId: string, group?: string, after?: number, limit?: number, options?: RawAxiosRequestConfig): AxiosPromise<ClientsClientIdTopicsGet200Response> {
            return localVarFp.clientsClientIdTopicsGet(clientId, group, after, limit, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {string} clientId 
         * @param {number} topicId 
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        clientsClientIdTopicsTopicIdWallpapersGet(clientId: string, topicId: number, size?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<ClientsClientIdTopicsTopicIdWallpapersGet200Response> {
            return localVarFp.clientsClientIdTopicsTopicIdWallpapersGet(clientId, topicId, size, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取原始图片
         * @param {string} key 
         * @param {string} cFTurnstileResponse 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        imageKeyGet(key: string, cFTurnstileResponse: string, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.imageKeyGet(key, cFTurnstileResponse, options).then((request) => request(axios, basePath));
        },
        /**
         * 图片为等比例缩放
         * @summary 获取壁纸列表，不包含原始图片地址，所以作为预览展示使用。
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {WallpapersGetSizedForEnum} [sizedFor] [filter] 根据尺寸进行过滤
         * @param {string} [color] [filter] 根据色系进行过滤
         * @param {string} [tag] [filter] 根据标签进行过滤
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        wallpapersGet(size?: Array<string>, page?: number, pageSize?: number, sizedFor?: WallpapersGetSizedForEnum, color?: string, tag?: string, options?: RawAxiosRequestConfig): AxiosPromise<WallpapersGet200Response> {
            return localVarFp.wallpapersGet(size, page, pageSize, sizedFor, color, tag, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取当前壁纸的相关其他壁纸 (固定数量的)
         * @param {string} key 使用 content_md5 值
         * @param {number} [num] 返回的数据量最大条数
         * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        wallpapersKeyRelatedGet(key: string, num?: number, size?: Array<string>, options?: RawAxiosRequestConfig): AxiosPromise<WallpapersKeyRelatedGet200Response> {
            return localVarFp.wallpapersKeyRelatedGet(key, num, size, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DefaultApi - object-oriented interface
 * @export
 * @class DefaultApi
 * @extends {BaseAPI}
 */
export class DefaultApi extends BaseAPI {
    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public categoriesColorsGet(options?: RawAxiosRequestConfig) {
        return DefaultApiFp(this.configuration).categoriesColorsGet(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public categoriesTagsGet(options?: RawAxiosRequestConfig) {
        return DefaultApiFp(this.configuration).categoriesTagsGet(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} clientId 
     * @param {string} [group] 
     * @param {number} [after] 使用 client_topic.id，而不是 topic_id
     * @param {number} [limit] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public clientsClientIdTopicsGet(clientId: string, group?: string, after?: number, limit?: number, options?: RawAxiosRequestConfig) {
        return DefaultApiFp(this.configuration).clientsClientIdTopicsGet(clientId, group, after, limit, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {string} clientId 
     * @param {number} topicId 
     * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public clientsClientIdTopicsTopicIdWallpapersGet(clientId: string, topicId: number, size?: Array<string>, options?: RawAxiosRequestConfig) {
        return DefaultApiFp(this.configuration).clientsClientIdTopicsTopicIdWallpapersGet(clientId, topicId, size, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 获取原始图片
     * @param {string} key 
     * @param {string} cFTurnstileResponse 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public imageKeyGet(key: string, cFTurnstileResponse: string, options?: RawAxiosRequestConfig) {
        return DefaultApiFp(this.configuration).imageKeyGet(key, cFTurnstileResponse, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 图片为等比例缩放
     * @summary 获取壁纸列表，不包含原始图片地址，所以作为预览展示使用。
     * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {WallpapersGetSizedForEnum} [sizedFor] [filter] 根据尺寸进行过滤
     * @param {string} [color] [filter] 根据色系进行过滤
     * @param {string} [tag] [filter] 根据标签进行过滤
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public wallpapersGet(size?: Array<string>, page?: number, pageSize?: number, sizedFor?: WallpapersGetSizedForEnum, color?: string, tag?: string, options?: RawAxiosRequestConfig) {
        return DefaultApiFp(this.configuration).wallpapersGet(size, page, pageSize, sizedFor, color, tag, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 获取当前壁纸的相关其他壁纸 (固定数量的)
     * @param {string} key 使用 content_md5 值
     * @param {number} [num] 返回的数据量最大条数
     * @param {Array<string>} [size] 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public wallpapersKeyRelatedGet(key: string, num?: number, size?: Array<string>, options?: RawAxiosRequestConfig) {
        return DefaultApiFp(this.configuration).wallpapersKeyRelatedGet(key, num, size, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const WallpapersGetSizedForEnum = {
    Desktop: 'desktop',
    Mobile: 'mobile'
} as const;
export type WallpapersGetSizedForEnum = typeof WallpapersGetSizedForEnum[keyof typeof WallpapersGetSizedForEnum];


