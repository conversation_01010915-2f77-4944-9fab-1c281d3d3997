# CategoriesColorsGet200ResponseColorsInner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**value** | **string** |  | [default to undefined]
**RGB** | **string** |  | [default to undefined]

## Example

```typescript
import { CategoriesColorsGet200ResponseColorsInner } from './api';

const instance: CategoriesColorsGet200ResponseColorsInner = {
    value,
    RGB,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
