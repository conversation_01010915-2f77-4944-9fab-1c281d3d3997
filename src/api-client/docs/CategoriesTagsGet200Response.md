# CategoriesTagsGet200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**tags** | [**Array&lt;CategoriesTagsGet200ResponseTagsInner&gt;**](CategoriesTagsGet200ResponseTagsInner.md) |  | [default to undefined]

## Example

```typescript
import { CategoriesTagsGet200Response } from './api';

const instance: CategoriesTagsGet200Response = {
    tags,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
