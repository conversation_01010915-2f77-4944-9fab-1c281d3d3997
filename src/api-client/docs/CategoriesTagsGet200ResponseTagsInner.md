# CategoriesTagsGet200ResponseTagsInner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **string** |  | [default to undefined]
**posterUrl** | **string** |  | [default to undefined]

## Example

```typescript
import { CategoriesTagsGet200ResponseTagsInner } from './api';

const instance: CategoriesTagsGet200ResponseTagsInner = {
    name,
    posterUrl,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
