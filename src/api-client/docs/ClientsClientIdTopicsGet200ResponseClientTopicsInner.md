# ClientsClientIdTopicsGet200ResponseClientTopicsInner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [default to undefined]
**title** | **string** |  | [default to undefined]
**topic_id** | **number** |  | [default to undefined]

## Example

```typescript
import { ClientsClientIdTopicsGet200ResponseClientTopicsInner } from './api';

const instance: ClientsClientIdTopicsGet200ResponseClientTopicsInner = {
    id,
    title,
    topic_id,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
