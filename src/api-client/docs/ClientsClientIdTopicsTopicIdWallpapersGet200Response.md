# ClientsClientIdTopicsTopicIdWallpapersGet200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**client_topic** | [**ClientsClientIdTopicsGet200ResponseClientTopicsInner**](ClientsClientIdTopicsGet200ResponseClientTopicsInner.md) |  | [default to undefined]
**wallpapers** | [**Array&lt;WallpapersGet200ResponseWallpapersInner&gt;**](WallpapersGet200ResponseWallpapersInner.md) |  | [default to undefined]

## Example

```typescript
import { ClientsClientIdTopicsTopicIdWallpapersGet200Response } from './api';

const instance: ClientsClientIdTopicsTopicIdWallpapersGet200Response = {
    client_topic,
    wallpapers,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
