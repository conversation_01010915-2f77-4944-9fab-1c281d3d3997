# DashPublishedTopicsGet200ResponsePublishedInnerClient


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **string** |  | [default to undefined]
**name** | **string** |  | [default to undefined]

## Example

```typescript
import { DashPublishedTopicsGet200ResponsePublishedInnerClient } from './api';

const instance: DashPublishedTopicsGet200ResponsePublishedInnerClient = {
    id,
    name,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
