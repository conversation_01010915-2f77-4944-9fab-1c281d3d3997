# DashSearchWallpapersGet200ResponseResultsInner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [default to undefined]
**wallpaper** | [**DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner**](DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner.md) |  | [default to undefined]

## Example

```typescript
import { DashSearchWallpapersGet200ResponseResultsInner } from './api';

const instance: DashSearchWallpapersGet200ResponseResultsInner = {
    id,
    wallpaper,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
