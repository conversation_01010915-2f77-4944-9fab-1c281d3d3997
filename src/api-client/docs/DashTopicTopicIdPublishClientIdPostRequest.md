# DashTopicTopicIdPublishClientIdPostRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**title** | **string** |  | [default to undefined]

## Example

```typescript
import { DashTopicTopicIdPublishClientIdPostRequest } from './api';

const instance: DashTopicTopicIdPublishClientIdPostRequest = {
    title,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
