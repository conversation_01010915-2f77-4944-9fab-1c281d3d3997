# DashTopicpublishedGet200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**published** | [**Array&lt;DashTopicpublishedGet200ResponsePublishedInner&gt;**](DashTopicpublishedGet200ResponsePublishedInner.md) |  | [default to undefined]
**current_page** | **number** |  | [default to undefined]
**current_page_size** | **number** |  | [default to undefined]
**total** | **number** |  | [default to undefined]

## Example

```typescript
import { DashTopicpublishedGet200Response } from './api';

const instance: DashTopicpublishedGet200Response = {
    published,
    current_page,
    current_page_size,
    total,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
