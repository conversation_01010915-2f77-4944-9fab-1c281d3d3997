# DashTopicpublishedGet200ResponsePublishedInner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [default to undefined]
**title** | **string** |  | [default to undefined]
**topic** | [**DashTopicsPost200Response**](DashTopicsPost200Response.md) |  | [default to undefined]
**client** | [**DashTopicpublishedGet200ResponsePublishedInnerClient**](DashTopicpublishedGet200ResponsePublishedInnerClient.md) |  | [default to undefined]
**published_at** | **string** |  | [default to undefined]

## Example

```typescript
import { DashTopicpublishedGet200ResponsePublishedInner } from './api';

const instance: DashTopicpublishedGet200ResponsePublishedInner = {
    id,
    title,
    topic,
    client,
    published_at,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
