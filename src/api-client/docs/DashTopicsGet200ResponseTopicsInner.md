# DashTopicsGet200ResponseTopicsInner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [default to undefined]
**comment** | **string** |  | [default to undefined]
**wallpaper_count** | **number** |  | [default to undefined]
**published_count** | **number** | 已发布统计 | [default to undefined]

## Example

```typescript
import { DashTopicsGet200ResponseTopicsInner } from './api';

const instance: DashTopicsGet200ResponseTopicsInner = {
    id,
    comment,
    wallpaper_count,
    published_count,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
