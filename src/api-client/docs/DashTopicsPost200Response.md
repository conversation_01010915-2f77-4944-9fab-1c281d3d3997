# DashTopicsPost200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [default to undefined]
**comment** | **string** |  | [default to undefined]

## Example

```typescript
import { DashTopicsPost200Response } from './api';

const instance: DashTopicsPost200Response = {
    id,
    comment,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
