# DashTopicsTopicIdSuggestionsTagsGet200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**tags** | **Array&lt;string&gt;** |  | [default to undefined]

## Example

```typescript
import { DashTopicsTopicIdSuggestionsTagsGet200Response } from './api';

const instance: DashTopicsTopicIdSuggestionsTagsGet200Response = {
    tags,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
