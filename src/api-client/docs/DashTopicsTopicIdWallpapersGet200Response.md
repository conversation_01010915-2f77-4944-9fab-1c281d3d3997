# DashTopicsTopicIdWallpapersGet200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**topic** | [**DashTopicsPost200Response**](DashTopicsPost200Response.md) |  | [default to undefined]
**wallpapers** | [**Array&lt;DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner&gt;**](DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner.md) |  | [default to undefined]
**current_page** | **number** |  | [default to undefined]
**current_page_size** | **number** |  | [default to undefined]
**total** | **number** |  | [default to undefined]

## Example

```typescript
import { DashTopicsTopicIdWallpapersGet200Response } from './api';

const instance: DashTopicsTopicIdWallpapersGet200Response = {
    topic,
    wallpapers,
    current_page,
    current_page_size,
    total,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
