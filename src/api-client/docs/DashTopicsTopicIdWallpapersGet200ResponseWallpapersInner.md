# DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**images** | [**WallpapersGet200ResponseWallpapersInnerImages**](WallpapersGet200ResponseWallpapersInnerImages.md) |  | [default to undefined]
**format** | **string** |  | [default to undefined]
**width** | **number** |  | [default to undefined]
**height** | **number** |  | [default to undefined]
**filesize** | **number** | 单位为字节 | [default to undefined]
**content_md5** | **string** |  | [default to undefined]
**id** | **number** |  | [default to undefined]

## Example

```typescript
import { DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner } from './api';

const instance: DashTopicsTopicIdWallpapersGet200ResponseWallpapersInner = {
    images,
    format,
    width,
    height,
    filesize,
    content_md5,
    id,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
