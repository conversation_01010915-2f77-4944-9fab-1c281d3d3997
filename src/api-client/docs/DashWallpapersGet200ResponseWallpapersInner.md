# DashWallpapersGet200ResponseWallpapersInner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **number** |  | [default to undefined]
**images** | [**WallpapersGet200ResponseWallpapersInnerImages**](WallpapersGet200ResponseWallpapersInnerImages.md) |  | [default to undefined]
**related_topic_count** | **number** | 关联话题数量 | [default to undefined]

## Example

```typescript
import { DashWallpapersGet200ResponseWallpapersInner } from './api';

const instance: DashWallpapersGet200ResponseWallpapersInner = {
    id,
    images,
    related_topic_count,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
