# DashWallpapersWallpaperIdTopicsGet200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**topics** | [**Array&lt;DashTopicsPost200Response&gt;**](DashTopicsPost200Response.md) |  | [default to undefined]

## Example

```typescript
import { DashWallpapersWallpaperIdTopicsGet200Response } from './api';

const instance: DashWallpapersWallpaperIdTopicsGet200Response = {
    topics,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
