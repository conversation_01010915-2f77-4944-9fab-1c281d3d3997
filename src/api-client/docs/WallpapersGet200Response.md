# WallpapersGet200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**wallpapers** | [**Array&lt;WallpapersGet200ResponseWallpapersInner&gt;**](WallpapersGet200ResponseWallpapersInner.md) |  | [default to undefined]
**current_page** | **number** |  | [default to undefined]
**current_page_size** | **number** |  | [default to undefined]
**total** | **number** |  | [default to undefined]

## Example

```typescript
import { WallpapersGet200Response } from './api';

const instance: WallpapersGet200Response = {
    wallpapers,
    current_page,
    current_page_size,
    total,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
