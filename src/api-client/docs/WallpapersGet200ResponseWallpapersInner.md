# WallpapersGet200ResponseWallpapersInner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**images** | [**WallpapersGet200ResponseWallpapersInnerImages**](WallpapersGet200ResponseWallpapersInnerImages.md) |  | [default to undefined]
**format** | **string** |  | [default to undefined]
**width** | **number** |  | [default to undefined]
**height** | **number** |  | [default to undefined]
**filesize** | **number** | 单位为字节 | [default to undefined]
**content_md5** | **string** |  | [default to undefined]
**uploader** | [**WallpapersGet200ResponseWallpapersInnerUploader**](WallpapersGet200ResponseWallpapersInnerUploader.md) |  | [default to undefined]

## Example

```typescript
import { WallpapersGet200ResponseWallpapersInner } from './api';

const instance: WallpapersGet200ResponseWallpapersInner = {
    images,
    format,
    width,
    height,
    filesize,
    content_md5,
    uploader,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
