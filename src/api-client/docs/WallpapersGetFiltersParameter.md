# WallpapersGetFiltersParameter


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**sized_for** | **string** |  | [optional] [default to undefined]
**color** | **string** |  | [optional] [default to undefined]
**tag** | **string** |  | [optional] [default to undefined]

## Example

```typescript
import { WallpapersGetFiltersParameter } from './api';

const instance: WallpapersGetFiltersParameter = {
    sized_for,
    color,
    tag,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
