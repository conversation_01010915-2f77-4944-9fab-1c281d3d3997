import axios, { AxiosError } from 'axios'
import { Configuration, DashboardApi } from './api-client'
import { API_ERROR_EVENT, LOGIN_NEXT_KEY } from './variables'
import router, { RouteName } from './router'

declare module 'axios' {
  export interface AxiosRequestConfig {
    skipCheckStatusCodes?: number[]
    /**
     * 设置为true 表明接口无需身份验证，默认 false。
     */
    noAuth?: boolean
  }
}

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
})

const auth = {
  accessToken: undefined,
  setAccessToken(token) {
    this.accessToken = token
    this.accessTokenExpiresAt = Date.now() + 3 * 60 * 1000
  },
  get isValid() {
    return !!(
      this.accessToken &&
      this.accessTokenExpiresAt &&
      this.accessTokenExpiresAt > Date.now()
    )
  },
} satisfies {
  accessToken?: string
  accessTokenExpiresAt?: number
  setAccessToken: (token: string) => void
  get isValid(): boolean
}

axiosInstance.interceptors.request.use(async (config) => {
  if (!config.noAuth && !auth.isValid) {
    await api
      .dashAuthTokenRefreshGet({ skipCheckStatusCodes: [401], noAuth: true })
      .then((resp) => {
        auth.setAccessToken(resp.data.access_token)
      })
      .catch((err: AxiosError) => {
        if (err.response?.status === 401) {
          router.replace({
            name: RouteName.Login,
            query: { [LOGIN_NEXT_KEY]: router.currentRoute.value.fullPath },
          })
          return Promise.reject(err)
        }
      })
  }

  if (auth.accessToken) {
    config.headers.Authorization = `Bearer ${auth.accessToken}`
  }
  return config
})
axiosInstance.interceptors.response.use(
  function (response) {
    return response
  },
  function (error: AxiosError) {
    if (error.response) {
      if (!error.config?.skipCheckStatusCodes?.includes(error.response.status)) {
        window.dispatchEvent(new CustomEvent(API_ERROR_EVENT, { detail: error.response }))
      }
    }
    return Promise.reject(error)
  },
)

export async function login(username: string, password: string, cFTurnstileResponse: string) {
  return api
    .dashAuthTokenPost(cFTurnstileResponse, username, password, {
      skipCheckStatusCodes: [401],
      noAuth: true,
    })
    .then((resp) => {
      auth.setAccessToken(resp.data.access_token)
    })
}

const configuration = new Configuration()
export const api = new DashboardApi(configuration, undefined, axiosInstance)
