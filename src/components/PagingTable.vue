<script lang="ts" setup>
import DataTable, { type DataTableProps } from 'primevue/datatable'
import { Paginator } from 'primevue'

const { rows, totalRecords, ...props } = defineProps<DataTableProps>()
const currentPage = defineModel<number>('currentPage', { default: 1 })
const emit = defineEmits(['update:currentPage'])
</script>

<template>
  <DataTable v-bind="props" tableStyle="min-width: 50rem" showHeaders>
    <slot />
    <template #empty>
      <div class="flex min-h-24 items-center justify-center">
        {{ props.value?.length === 0 ? '没有数据' : '加载中...' }}
      </div>
    </template>
    <template
      #footer
      v-if="rows !== undefined && totalRecords !== undefined && totalRecords > rows"
    >
      <Paginator
        :first="(currentPage - 1) * (rows ?? 0)"
        @page="
          ({ page }) => {
            emit('update:currentPage', page + 1)
          }
        "
        :rows="rows"
        :totalRecords="totalRecords"
      ></Paginator>
    </template>
  </DataTable>
</template>
