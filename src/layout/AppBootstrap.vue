<script lang="ts" setup>
import { api } from '@/api'
import AppLayout from './AppLayout.vue'
import { onMounted, ref } from 'vue'

const loading = ref(true)

onMounted(() => {
  api.dashUserinfoGet().then(() => {
    loading.value = false
  })
})
</script>

<template>
  <AppLayout v-if="!loading"></AppLayout>
  <template v-else>
    <div class="flex h-screen w-screen items-center justify-center">
      <i class="pi pi-spin pi-spinner text-3xl!"></i>
    </div>
  </template>
</template>
