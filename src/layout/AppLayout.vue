<script setup lang="ts">
import AppBreadcurmb from './AppBreadcurmb.vue'
import AppTools from './AppTools.vue'
import AppMenu from './AppMenu.vue'
import Divider from 'primevue/divider'
</script>

<template>
  <div class="flex h-[100vh] space-x-3 p-3">
    <AppMenu />
    <div class="border-surface grow overflow-hidden rounded-md border">
      <div class="flex h-full flex-col *:shrink-0">
        <AppTools />
        <Divider class="m-0!"></Divider>
        <AppBreadcurmb></AppBreadcurmb>
        <Divider class="z-10 m-0!"></Divider>
        <main class="rounded-border border-surface h-0 grow overflow-y-scroll p-4">
          <RouterView></RouterView>
        </main>
      </div>
    </div>
  </div>
</template>
