<script lang="ts" setup>
import Button from 'primevue/button'
import { ref } from 'vue'
import Popover from 'primevue/popover'
import { usePrimaryColor } from '@/utils/palette'

const op = ref()
const toggle = (event: Event) => {
  op.value.toggle(event)
}

const primaryColors = [
  'emerald',
  'green',
  'lime',
  'red',
  'orange',
  'amber',
  'yellow',
  'teal',
  'cyan',
  'sky',
  'blue',
  'indigo',
  'violet',
  'purple',
  'fuchsia',
  'pink',
  'rose',
]

const primaryColor = usePrimaryColor()
</script>

<template>
  <Button class="aspect-square" icon="pi pi-palette" @click="toggle"></Button>
  <Popover ref="op" :style="{ maxWidth: '20rem' }">
    <div>
      <span>主题色</span>
      <div class="flex flex-wrap gap-2 py-2">
        <span
          v-for="color in primaryColors"
          :key="color"
          :style="{ backgroundColor: `var(--p-${color}-500)` }"
          :class="[
            'size-6',
            'cursor-pointer',
            'rounded-full',
            primaryColor === color && 'outline-primary outline-2 outline-offset-2',
          ]"
          @click="primaryColor = color"
        ></span>
      </div>
    </div>
  </Popover>
</template>
