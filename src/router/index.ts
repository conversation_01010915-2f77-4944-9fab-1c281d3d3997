import { createRouter, createWeb<PERSON><PERSON>ory, RouterView, type RouteRecordRaw } from 'vue-router'
import AppBootstrap from '@/layout/AppBootstrap.vue'
import LoginView from '@/views/LoginView.vue'
import type { MenuItem } from 'primevue/menuitem'

export const RouteName = {
  Login: Symbol(),
  TopicWallpapers: Symbol(),
  TopicPublished: Symbol(),
}

export const menuRoutes: RouteRecordRaw[] = [
  {
    path: '/wallpapers',
    component: RouterView,
    meta: {
      label: '壁纸',
    },
    children: [
      {
        path: '',
        component: () => import('@/views/WallpaperListView.vue'),
        meta: {
          label: '壁纸列表',
          icon: 'pi pi-image',
        } satisfies MenuItem,
      },
      {
        path: 'upload',
        component: () => import('@/views/UploadWallapersView.vue'),
        meta: {
          label: '上传壁纸',
          icon: 'pi pi-upload',
        } satisfies MenuItem,
      },
    ],
  },

  {
    path: '/topics',
    component: RouterView,
    meta: {
      label: '专题',
    } satisfies MenuItem,
    children: [
      {
        path: '',
        component: () => import('@/views/TopicListView.vue'),
        meta: {
          label: '专题管理',
          icon: 'pi pi-th-large',
        },
      },
      {
        name: RouteName.TopicWallpapers,
        path: ':id/wallpapers',
        component: () => import('@/views/TopicWallpaperListView.vue'),
        meta: {
          label: '专题壁纸',
          hidden: true,
        } satisfies MenuItem,
      },
      {
        name: RouteName.TopicPublished,
        path: 'published',
        component: () => import('@/views/TopicPublisedListView.vue'),
        meta: {
          label: '发布管理',
          icon: 'pi pi-send',
        } satisfies MenuItem,
      },
    ],
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: AppBootstrap,
      children: menuRoutes,
    },
    { path: '/login', name: RouteName.Login, component: LoginView },
  ],
})

export default router
