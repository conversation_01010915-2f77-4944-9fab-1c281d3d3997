import { updatePrimaryPalette } from '@primeuix/themes'
import { ref, watch } from 'vue'

const storageKey = 'primaryColor'

export function changePrimaryColor(color: string) {
  updatePrimaryPalette({
    50: `{${color}.50}`,
    100: `{${color}.100}`,
    200: `{${color}.200}`,
    300: `{${color}.300}`,
    400: `{${color}.400}`,
    500: `{${color}.500}`,
    600: `{${color}.600}`,
    700: `{${color}.700}`,
    800: `{${color}.800}`,
    900: `{${color}.900}`,
    950: `{${color}.950}`,
  })
}

function getStoragePrimaryColor(): string {
  return localStorage.getItem(storageKey) || 'cyan'
}

export function usePrimaryColor() {
  const primaryColor = ref<string>(getStoragePrimaryColor())
  watch(primaryColor, () => {
    if (primaryColor.value) {
      localStorage.setItem(storageKey, primaryColor.value)
      changePrimaryColor(primaryColor.value)
    }
  })
  return primaryColor
}

export function initializePrimaryColor() {
  const primaryColor = getStoragePrimaryColor()
  changePrimaryColor(primaryColor)
}
