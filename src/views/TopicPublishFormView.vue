<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { Form, FormField, FormFieldMessage } from '@/components/primevue'
import Select from 'primevue/select'
import { z } from 'zod'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { Button, InputText } from 'primevue'
import type { FormSubmitEvent } from '@primevue/forms'
import type { DashTopicpublishedGet200ResponsePublishedInnerClient } from '@/api-client'
import { api } from '@/api'

const emit = defineEmits({
  save(values: z.infer<typeof schema>) {
    return values
  },
})

const schema = z.object({
  clientId: z.preprocess((x) => x ?? '', z.string().nonempty('请选择客户端')),
  title: z.preprocess(
    (x) => x ?? '',
    z.string().min(1, '请输入标题').max(100, '标题长度不能超过 100 个字符'),
  ),
})
type Schema = z.infer<typeof schema>
const resolver = zodResolver(schema)
const props = defineProps<{
  initialValues?: Schema
  saveLabel: string
  saveIcon?: string
  disabledClientId?: boolean
}>()

const clientOptions = ref<DashTopicpublishedGet200ResponsePublishedInnerClient[]>()
onMounted(() => {
  api.dashClientsGet().then((resp) => {
    clientOptions.value = resp.data.clients
  })
})

function handleSubmit({ values, valid }: FormSubmitEvent) {
  if (!valid) return
  emit('save', values as Schema)
}
</script>

<template>
  <Form :initialValues="props.initialValues" :resolver="resolver" @submit="handleSubmit">
    <FormField v-slot="$field" name="clientId">
      <label>客户端</label>
      <Select
        :options="clientOptions"
        optionLabel="name"
        optionValue="id"
        placeholder="请选择客户端"
        :disabled="props.disabledClientId"
      >
        <template #option="slotProps">
          <div>{{ slotProps.option.name }}</div>
        </template>
      </Select>
      <FormFieldMessage v-if="$field?.invalid">{{ $field.error?.message }}</FormFieldMessage>
    </FormField>
    <FormField v-slot="$field" name="title">
      <label>标题</label>
      <InputText placeholder="请输入标题"></InputText>
      <FormFieldMessage v-if="$field?.invalid">{{ $field.error?.message }}</FormFieldMessage>
    </FormField>
    <Button type="submit" :label="props.saveLabel" :icon="props.saveIcon" fluid></Button>
  </Form>
</template>
