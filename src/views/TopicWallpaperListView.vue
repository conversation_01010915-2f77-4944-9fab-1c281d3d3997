<script lang="ts" setup>
import { api } from '@/api'
import Image from 'primevue/image'
import { onBeforeMount, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import PagingTable from '@/components/PagingTable.vue'
import Column from 'primevue/column'
import { Button, Dialog, InputText } from 'primevue'
import Tag from 'primevue/tag'
import { useConfirm } from '@/components/primevue'
import type {
  DashSearchWallpapersGet200Response,
  DashTopicsTopicIdWallpapersGet200Response,
} from '@/api-client'

const confirm = useConfirm()

const route = useRoute()
const currentTopicId = Number(route.params.id)

const currentPage = ref(1)
const viewData = ref<DashTopicsTopicIdWallpapersGet200Response>()
const tableLoading = ref(true)

function updateViewData() {
  tableLoading.value = true
  api
    .dashTopicsTopicIdWallpapersGet(currentTopicId, currentPage.value, 20)
    .then((resp) => {
      viewData.value = resp.data
    })
    .finally(() => {
      tableLoading.value = false
    })
}
onBeforeMount(() => {
  updateViewData()
})
watch(currentPage, () => {
  updateViewData()
})

const dialogVisiable = ref(false)

const searchText = ref('')
const searchResult = ref<DashSearchWallpapersGet200Response>()
const searchLoading = ref(false)
const addedWallpaperIds = ref(new Set())
const searchSuggestedTags = ref<string[]>([])
const searchNoMore = ref(false)

function searchWallpapersWrapper(query: string, after?: number) {
  const limit = 20

  return api
    .dashSearchWallpapersGet(query, after, limit, undefined, currentTopicId)
    .then((resp) => {
      searchNoMore.value = resp.data.results.length < limit
      return resp.data
    })
}

function handleSearch() {
  if (!searchText.value) return
  searchLoading.value = true
  searchWallpapersWrapper(searchText.value)
    .then((data) => {
      searchResult.value = data
    })
    .finally(() => {
      searchLoading.value = false
    })
}
function handleClickSuggestedTag(tag: string) {
  searchText.value = tag
  handleSearch()
}
</script>

<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <div>
        <label>专题描述: </label>
        <span>{{ viewData?.topic.comment }}</span>
      </div>
      <Button
        label="添加壁纸"
        icon="pi pi-plus"
        @click="
          () => {
            dialogVisiable = true
          }
        "
      ></Button>
    </div>
    <PagingTable
      :value="viewData?.wallpapers"
      v-model:currentPage="currentPage"
      :rows="viewData?.current_page_size"
      :total-records="viewData?.total"
      :loading="tableLoading"
    >
      <Column header="图片">
        <template #body="{ data }">
          <Image image-class="size-24 object-contain" :src="data.images['default']" />
        </template>
      </Column>
      <Column header="操作">
        <template #body="{ data }">
          <Button
            icon="pi pi-trash"
            severity="danger"
            rounded
            size="small"
            @click="
              () => {
                confirm.require({
                  message: '确定要删除吗？',
                  accept: () => {
                    api
                      .dashTopicsTopicIdWallpapersWallpaperIdDelete(currentTopicId, data.id)
                      .then(() => {
                        updateViewData()
                      })
                  },
                })
              }
            "
          ></Button>
        </template>
      </Column>
    </PagingTable>
  </div>

  <Dialog
    header="添加壁纸"
    :modal="true"
    v-model:visible="dialogVisiable"
    :style="{ width: '60rem' }"
    @show="
      () => {
        api.dashTopicsTopicIdSuggestionsTagsGet(currentTopicId).then((resp) => {
          searchSuggestedTags = resp.data.tags
        })
      }
    "
    @after-hide="
      () => {
        searchText = ''
        searchResult = undefined

        if (addedWallpaperIds.size) {
          // 如果添加了图片，需要刷新列表
          if (currentPage !== 1) {
            currentPage = 1 // 如果不是第一页，直接改到第一页，watch 会负责刷新列表
          } else {
            updateViewData() // 如果是第一页，则手动刷新列表
          }
          addedWallpaperIds.clear()
        }
      }
    "
  >
    <div class="flex items-center gap-2">
      <InputText
        v-model.trim="searchText"
        placeholder="输入关键词进行搜索"
        fluid
        @keydown.enter="handleSearch"
        :disabled="searchLoading"
      ></InputText>
      <Button
        :loading="searchLoading"
        class="shrink-0"
        icon="pi pi-search"
        label="搜索"
        :disabled="!searchText"
        @click="handleSearch"
      ></Button>
    </div>
    <!-- suggestions -->
    <div v-show="searchSuggestedTags.length" class="mt-4 flex gap-2">
      <Tag
        class="cursor-pointer"
        v-for="tag in searchSuggestedTags"
        :key="tag"
        @click="handleClickSuggestedTag(tag)"
      >
        {{ tag }}
      </Tag>
    </div>
    <div>
      <div v-show="searchResult?.results.length">
        <ul class="grid grid-cols-4 gap-2 py-4">
          <li
            class="flex items-center justify-center"
            v-for="item in searchResult?.results"
            :key="item.id"
          >
            <div class="border-surface group relative size-52 overflow-hidden border">
              <img class="h-full w-full object-contain" :src="item.wallpaper.images['default']" />
              <button
                v-show="!addedWallpaperIds.has(item.wallpaper.id)"
                class="bg-primary hover:bg-primary-600 absolute top-0 right-0 hidden size-8 cursor-pointer text-white group-hover:block"
                @click="
                  () => {
                    api
                      .dashTopicsTopicIdWallpapersWallpaperIdPut(currentTopicId, item.wallpaper.id)
                      .then(() => {
                        addedWallpaperIds.add(item.wallpaper.id)
                      })
                  }
                "
              >
                <span class="pi pi-plus"></span>
              </button>

              <!-- overlay -->
              <div
                v-show="addedWallpaperIds.has(item.wallpaper.id)"
                class="absolute top-0 left-0 flex h-full w-full items-center justify-center bg-black/70"
              >
                <span class="pi pi-check text-primary text-4xl!"></span>
              </div>
            </div>
          </li>
        </ul>
        <div class="flex justify-center">
          <Button
            size="small"
            variant="text"
            :severity="searchNoMore ? 'secondary' : undefined"
            :label="searchNoMore ? '没有更多了' : '加载更多'"
            :disabled="searchNoMore"
            @click="
              () => {
                if (searchResult === undefined) return
                searchWallpapersWrapper(
                  searchResult.q,
                  searchResult.results[searchResult.results.length - 1].id,
                ).then((data) => {
                  searchResult?.results.push(...data.results)
                })
              }
            "
          ></Button>
        </div>
      </div>
      <div class="mt-2" v-show="searchResult?.results.length === 0 && !searchLoading">
        未搜索到相关内容
      </div>
    </div>
  </Dialog>
</template>
