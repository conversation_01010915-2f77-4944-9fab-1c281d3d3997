<script setup lang="ts">
import { api } from '@/api'
import type {
  DashTopicsPost200Response,
  DashWallpapersGet200Response,
  DashWallpapersGet200ResponseWallpapersInner,
} from '@/api-client'
import PagingTable from '@/components/PagingTable.vue'
import { RouteName } from '@/router'
import { Column, Tag, Dialog, DataTable, Button, Image, useDialog } from 'primevue'
import { ref, watch } from 'vue'
import TopicSearchView, { type TopicSearchViewProps } from './TopicSearchView.vue'

const dialog = useDialog()

const tableLoading = ref(true)
const viewData = ref<DashWallpapersGet200Response>()

const currentPage = ref(1)
function updateViewData() {
  tableLoading.value = true
  return api
    .dashWallpapersGet(currentPage.value, 20)
    .then((res) => {
      viewData.value = res.data
    })
    .finally(() => {
      tableLoading.value = false
    })
}
watch(
  currentPage,
  () => {
    updateViewData()
  },
  { immediate: true },
)

const dialogVisiable = ref(false)
const dialogTableValue = ref<DashTopicsPost200Response[]>()
const dialogTableLoading = ref(true)
</script>

<template>
  <PagingTable
    v-model:current-page="currentPage"
    :loading="tableLoading"
    :value="viewData?.wallpapers"
    :rows="viewData?.current_page_size"
    :total-records="viewData?.total"
    data-key="id"
  >
    <Column header="图片">
      <template #body="slotProps: { data: DashWallpapersGet200ResponseWallpapersInner }">
        <Image image-class="size-25 object-contain" :src="slotProps.data.images.default" preview />
      </template>
    </Column>
    <Column header="关联专题">
      <template #body="slotProps: { data: DashWallpapersGet200ResponseWallpapersInner }">
        <template v-if="slotProps.data.related_topic_count === 0">
          <Tag severity="secondary">0</Tag>
        </template>
        <button
          v-else
          class="cursor-pointer"
          @click="
            () => {
              if (slotProps.data.related_topic_count === 0) return

              dialogVisiable = true
              dialogTableLoading = true
              api
                .dashWallpapersWallpaperIdTopicsGet(slotProps.data.id)
                .then((resp) => {
                  dialogTableValue = resp.data.topics
                })
                .finally(() => {
                  dialogTableLoading = false
                })
            }
          "
        >
          <Tag>{{ slotProps.data.related_topic_count }}</Tag>
        </button>
      </template>
    </Column>
    <Column>
      <template #body="slotProps">
        <Button
          variant="text"
          icon="pi pi-plus"
          rounded
          @click="
            () => {
              dialog.open(TopicSearchView, { data: { a: 123 } satisfies TopicSearchViewProps })
            }
          "
        ></Button>
      </template>
    </Column>
  </PagingTable>

  <Dialog
    header="关联专题"
    v-model:visible="dialogVisiable"
    class="min-w-200"
    @after-hide="
      () => {
        dialogTableValue = undefined
      }
    "
  >
    <DataTable :value="dialogTableValue" :loading="dialogTableLoading">
      <Column header="专题备注" field="comment"></Column>
      <Column>
        <template #body="slotProps: { data: DashTopicsPost200Response }">
          <Button asChild v-slot="slot" variant="link">
            <RouterLink
              :class="slot.class"
              :to="{ name: RouteName.TopicWallpapers, params: { id: slotProps.data.id } }"
            >
              前往专题
            </RouterLink>
          </Button>
        </template>
      </Column>
    </DataTable>
  </Dialog>
</template>
