/** Cloudflare Worker entry point */

const backendUrl = 'https://wallnest.oneproject.dev'

export default {
  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url)
    if (url.pathname.startsWith('/api/')) {
      const target = backendUrl + url.pathname + url.search

      const headers = new Headers(request.headers)
      headers.set('X-Forwarded-Prefix', '/api')

      const newRequest = new Request(target, {
        method: request.method,
        headers,
        body:
          request.method !== 'GET' && request.method !== 'HEAD' ? await request.text() : undefined,
        redirect: 'follow',
      })
      return fetch(newRequest)
    }
    return fetch(request)
  },
} satisfies ExportedHandler
